issues:
  - title: "[Task] Responsive design"
    description: |
      # Background
      The current interface needs to be accessible and functional across different screen sizes and devices. Users may access the system from desktops, tablets, and mobile devices, requiring a responsive design that adapts to various viewport sizes.

      # Acceptance Criteria
      - Interface adapts seamlessly to desktop, tablet, and mobile screen sizes
      - All functionality remains accessible on smaller screens
      - Navigation and buttons are appropriately sized for touch interfaces
      - Text remains readable without horizontal scrolling
      - Tables and data displays are responsive or provide horizontal scrolling where needed
      - Test across major browsers and device types
      - Ensure accessibility standards are maintained across all screen sizes
    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
    milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Vault check in the release progress bar"
  #   description: |
  #     # Background
  #     The release progress bar should include a step to verify that all required secrets and configurations are present in Vault before proceeding with deployment. This helps catch missing secrets early in the release process.

  #     # Acceptance Criteria
  #     - Add Vault verification step to the release progress bar
  #     - Check that all required secrets for the service are present in Vault
  #     - Display clear status (pass/fail) for Vault check
  #     - Provide detailed error messages when secrets are missing
  #     - Allow manual retry of Vault check if needed
  #     - Integrate with existing Vault API or CLI tools
  #     - Block release progression if critical secrets are missing
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Config Template Changes notification to remind devs to check Vault"
  #   description: |
  #     # Background
  #     When configuration templates are modified, developers need to be reminded to update corresponding secrets in Vault. This prevents deployment failures due to missing or outdated secrets.

  #     # Acceptance Criteria
  #     - Detect when configuration templates are changed in commits
  #     - Send notifications to relevant developers/teams
  #     - Include specific details about which config files were modified
  #     - Provide links to relevant Vault paths that may need updates
  #     - Allow configuration of notification recipients per service/team
  #     - Include instructions on how to update Vault secrets
  #     - Track acknowledgment of notifications
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Ability to see # of commits and # of files changed"
  #   description: |
  #     # Background
  #     Users need visibility into the scope of changes in a release to better understand the impact and risk level. Showing commit count and file change metrics helps with release assessment.

  #     # Acceptance Criteria
  #     - Display total number of commits in the release
  #     - Show total number of files changed
  #     - Break down file changes by type (added, modified, deleted)
  #     - Display this information prominently in the release view
  #     - Update counts in real-time as releases progress
  #     - Provide drill-down capability to see detailed file lists
  #     - Include comparison with previous releases for context
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Ability to see how many major, minor and patch versions are changing"
  #   description: |
  #     # Background
  #     Understanding the semantic versioning impact of a release helps teams assess risk and plan accordingly. Users need to see which services are having major, minor, or patch version changes.

  #     # Acceptance Criteria
  #     - Parse semantic version changes for each service in the release
  #     - Display count of services with major version changes
  #     - Display count of services with minor version changes
  #     - Display count of services with patch version changes
  #     - Highlight major version changes as higher risk
  #     - Provide breakdown by service showing version transitions
  #     - Include version change summary in release overview
  #     - Handle non-semantic versioning gracefully
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Auth0 access controls"
  #   description: |
  #     # Background
  #     Implement proper authentication and authorization using Auth0 to control access to the system. This ensures only authorized users can access sensitive release and deployment information.

  #     # Acceptance Criteria
  #     - Integrate Auth0 authentication for user login
  #     - Implement role-based access control (RBAC)
  #     - Define user roles (e.g., developer, release manager, admin)
  #     - Restrict access to sensitive operations based on user roles
  #     - Implement session management and token refresh
  #     - Add logout functionality
  #     - Ensure secure token storage and transmission
  #     - Provide user profile and role information in UI
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Only accessibly behind VPN"
  #   description: |
  #     # Background
  #     For security purposes, the system should only be accessible from within the corporate VPN to protect sensitive deployment and release information from external access.

  #     # Acceptance Criteria
  #     - Configure network access controls to require VPN connection
  #     - Implement IP allowlisting for VPN ranges
  #     - Add clear error messages for users trying to access without VPN
  #     - Document VPN requirements for users
  #     - Test access from both VPN and non-VPN connections
  #     - Ensure all endpoints are properly protected
  #     - Consider emergency access procedures if needed
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Ability to sort table by version"
  #   description: |
  #     # Background
  #     Users need to be able to sort release tables by version numbers to easily find specific releases or understand version progression. This improves usability when dealing with many releases.

  #     # Acceptance Criteria
  #     - Add sortable column headers for version fields
  #     - Implement semantic version sorting (not alphabetical)
  #     - Support both ascending and descending sort orders
  #     - Maintain sort state during page navigation
  #     - Handle mixed version formats gracefully
  #     - Provide visual indicators for current sort column and direction
  #     - Ensure sorting works with pagination
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Links to CircleCI and ArgoCD"
  #   description: |
  #     # Background
  #     Users need quick access to external tools like CircleCI for build information and ArgoCD for deployment details. Direct links eliminate the need to manually navigate to these systems.

  #     # Acceptance Criteria
  #     - Add direct links to CircleCI builds for each release
  #     - Add direct links to ArgoCD applications and sync status
  #     - Ensure links open in new tabs/windows
  #     - Handle cases where builds or deployments don't exist yet
  #     - Include visual indicators for link status (active, pending, failed)
  #     - Support deep linking to specific builds or deployment instances
  #     - Provide fallback behavior if external systems are unavailable
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Ability to filter by service"
  #   description: |
  #     # Background
  #     When dealing with multiple services, users need to filter the view to focus on specific services they're responsible for or interested in. This improves usability and reduces cognitive load.

  #     # Acceptance Criteria
  #     - Add service filter dropdown or search functionality
  #     - Support multi-select filtering for multiple services
  #     - Maintain filter state during navigation
  #     - Show clear indication of active filters
  #     - Provide "clear all filters" functionality
  #     - Update URL to reflect current filters for bookmarking
  #     - Ensure filtering works with other table features (sorting, pagination)
  #     - Include service count indicators in filter options
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Ability to view ArgoCD logs for a sync"
  #   description: |
  #     # Background
  #     When deployments fail or behave unexpectedly, users need access to ArgoCD sync logs to troubleshoot issues. This eliminates the need to switch to ArgoCD interface for log viewing.

  #     # Acceptance Criteria
  #     - Integrate with ArgoCD API to fetch sync logs
  #     - Display logs in a readable format within the interface
  #     - Support log filtering and searching
  #     - Show both successful and failed sync logs
  #     - Provide real-time log streaming for active syncs
  #     - Include timestamp and severity level information
  #     - Allow downloading logs for offline analysis
  #     - Handle authentication with ArgoCD properly
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Vault missing secrets notification"
  #   description: |
  #     # Background
  #     Proactively notify teams when required secrets are missing from Vault to prevent deployment failures. This helps catch configuration issues before they impact releases.

  #     # Acceptance Criteria
  #     - Scan Vault for missing secrets based on service requirements
  #     - Send notifications via appropriate channels (email, Slack, etc.)
  #     - Include specific details about which secrets are missing
  #     - Provide instructions on how to add missing secrets
  #     - Allow configuration of notification frequency and recipients
  #     - Track notification history and acknowledgments
  #     - Integrate with existing alerting systems
  #     - Support different notification urgency levels
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Failure messages that exist in current Release Notifications"
  #   description: |
  #     # Background
  #     Preserve and enhance the existing failure notification system to ensure teams are promptly informed about release issues with detailed context and actionable information.

  #     # Acceptance Criteria
  #     - Migrate existing failure message functionality to new system
  #     - Maintain all current notification channels and recipients
  #     - Preserve message formatting and content structure
  #     - Ensure no loss of notification reliability during transition
  #     - Add any missing failure scenarios from current system
  #     - Test notification delivery for all failure types
  #     - Document notification configuration and customization options
  #     - Provide backward compatibility during migration period
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Modal to view the changelog"
  #   description: |
  #     # Background
  #     Users need an easy way to view detailed changelog information for releases without navigating away from the main interface. A modal provides quick access to this information.

  #     # Acceptance Criteria
  #     - Create modal component for displaying changelog
  #     - Parse and format changelog from various sources (git, release notes, etc.)
  #     - Support markdown rendering for formatted changelogs
  #     - Include commit messages, pull request titles, and descriptions
  #     - Provide search functionality within changelog
  #     - Support different changelog formats and sources
  #     - Allow copying changelog content to clipboard
  #     - Ensure modal is responsive and accessible
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Auto-refresh every 15 seconds"
  #   description: |
  #     # Background
  #     Release and deployment status changes frequently, and users need up-to-date information without manual page refreshes. Auto-refresh ensures the interface stays current.

  #     # Acceptance Criteria
  #     - Implement automatic page refresh every 15 seconds
  #     - Preserve user interactions and form state during refresh
  #     - Show visual indicator when refresh is occurring
  #     - Allow users to pause/resume auto-refresh
  #     - Handle network errors gracefully during refresh
  #     - Optimize refresh to only update changed data
  #     - Ensure refresh doesn't interrupt user actions
  #     - Provide manual refresh option as backup
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Release progress tracker from build, staging, approval, ArgoCD and production"
  #   description: |
  #     # Background
  #     Users need a comprehensive view of the entire release pipeline from initial build through production deployment. A visual progress tracker helps understand current status and bottlenecks.

  #     # Acceptance Criteria
  #     - Create visual progress bar showing all release stages
  #     - Include stages: Build, Staging, Approval, ArgoCD Sync, Production
  #     - Show current status for each stage (pending, in-progress, completed, failed)
  #     - Display estimated time remaining for each stage
  #     - Provide detailed status information on hover or click
  #     - Include links to relevant external systems for each stage
  #     - Show historical timing data for similar releases
  #     - Handle parallel deployments and complex workflows
  #     - Update progress in real-time
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Button for approval w/o entering CircleCI"
  #   description: |
  #     # Background
  #     Release approvals currently require navigating to CircleCI, which adds friction to the approval process. A direct approval button streamlines the workflow for authorized users.

  #     # Acceptance Criteria
  #     - Add approval button directly in the release interface
  #     - Integrate with CircleCI API to trigger approvals
  #     - Implement proper authorization checks for approval permissions
  #     - Show approval status and who approved
  #     - Provide confirmation dialog before approving
  #     - Handle approval failures gracefully with error messages
  #     - Log approval actions for audit purposes
  #     - Support bulk approvals for multiple releases if applicable
  #     - Ensure approval button is only visible to authorized users
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"

  # - title: "[Task] Button for rollbacks w/o entering CircleCI"
  #   description: |
  #     # Background
  #     Emergency rollbacks currently require navigating to CircleCI, which can delay critical incident response. A direct rollback button enables faster response times.

  #     # Acceptance Criteria
  #     - Add rollback button directly in the release interface
  #     - Integrate with CircleCI API to trigger rollback workflows
  #     - Implement proper authorization checks for rollback permissions
  #     - Show confirmation dialog with rollback impact details
  #     - Provide rollback target selection (previous version, specific version)
  #     - Display rollback progress and status
  #     - Handle rollback failures with clear error messages
  #     - Log rollback actions for audit and incident tracking
  #     - Include emergency rollback procedures and escalation paths
  #     - Ensure rollback button is prominently placed for emergency use
  #   teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
  #   stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
  #   projectId: "9c790e38-576f-4657-8dae-1d2c8f5c2850"
  #   milestoneId: "c16c2337-0052-4048-b60d-0a9e2d3b6f4a"